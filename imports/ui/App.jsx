import React, { useEffect, useState } from 'react';
import { Landing } from './Landing.jsx';
import { Register } from './Register.jsx';

export const App = () => {
  const [theme, setTheme] = useState('light');
  const [page, setPage] = useState('landing');

  useEffect(() => {
    document.body.dataset.theme = theme;
  }, [theme]);

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  return (
    <div className="app">
      <button className="theme-toggle" onClick={toggleTheme} aria-label="Toggle theme">
        {theme === 'light' ? '🌙' : '☀️'}
      </button>
      {page === 'landing' && <Landing goToRegister={() => setPage('register')} />}
      {page === 'register' && <Register goToLanding={() => setPage('landing')} />}
    </div>
  );
};
