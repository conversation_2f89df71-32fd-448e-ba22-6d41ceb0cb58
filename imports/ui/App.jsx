import React, { useEffect, useState } from 'react';
import { useTracker } from 'meteor/react-meteor-data';
import { Meteor } from 'meteor/meteor';
import { Landing } from './Landing.jsx';
import { Register } from './Register.jsx';

export const App = () => {
  const [theme, setTheme] = useState('light');
  const [page, setPage] = useState('landing');

  // Track user authentication state
  const { user, isLoading } = useTracker(() => {
    const user = Meteor.user();
    const isLoading = !Meteor.loginServicesConfigured();
    return { user, isLoading };
  }, []);

  useEffect(() => {
    document.body.dataset.theme = theme;
  }, [theme]);

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  const handleLogout = () => {
    Meteor.logout();
    setPage('landing');
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="app">
        <div className="landing">
          <h1 className="title brand">Roylia</h1>
          <p className="tagline">Loading...</p>
        </div>
      </div>
    );
  }

  // Show dashboard if user is logged in
  if (user) {
    return (
      <div className="app">
        <button className="theme-toggle" onClick={toggleTheme} aria-label="Toggle theme">
          {theme === 'light' ? '🌙' : '☀️'}
        </button>
        <div className="landing">
          <h1 className="title brand">Welcome to Roylia</h1>
          <p className="tagline">Hello, {user.profile?.fullname || user.emails?.[0]?.address}!</p>
          <div className="card">
            <h2>Dashboard</h2>
            <p>You are successfully logged in!</p>
            <button className="btn" onClick={handleLogout}>Logout</button>
          </div>
        </div>
      </div>
    );
  }

  // Show login/register pages if user is not logged in
  return (
    <div className="app">
      <button className="theme-toggle" onClick={toggleTheme} aria-label="Toggle theme">
        {theme === 'light' ? '🌙' : '☀️'}
      </button>
      {page === 'landing' && <Landing goToRegister={() => setPage('register')} />}
      {page === 'register' && <Register goToLanding={() => setPage('landing')} />}
    </div>
  );
};
