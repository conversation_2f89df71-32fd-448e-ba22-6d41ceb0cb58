import React, { useState } from 'react';

export const Register = ({ goToLanding }) => {
  const [register, setRegister] = useState({ fullname: '', email: '', password: '', dob: '' });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setRegister({ ...register, [name]: value });
  };

  const handleRegister = (e) => {
    e.preventDefault();
    console.log('Register info', register);
  };

  return (
    <div className="landing">
      <h1 className="title brand"><PERSON><PERSON></h1>
      <p className="tagline">The social network for the mentally royal</p>
      <form className="card" onSubmit={handleRegister}>
        <h2>Register</h2>
        <label>
          Full Name
          <input
            type="text"
            name="fullname"
            value={register.fullname}
            onChange={handleChange}
            required
          />
        </label>
        <label>
          Email
          <input
            type="email"
            name="email"
            value={register.email}
            onChange={handleChange}
            required
          />
        </label>
        <label>
          Password
          <input
            type="password"
            name="password"
            value={register.password}
            onChange={handleChange}
            required
          />
        </label>
        <label>
          Date of Birth
          <input
            type="date"
            name="dob"
            value={register.dob}
            onChange={handleChange}
            required
          />
        </label>
        <button type="submit" className="btn">Register</button>
      </form>
      <div className="forget">
        <a href="#" onClick={goToLanding}>Back to Login</a>
      </div>
    </div>
  );
};
