import React, { useState } from 'react';
import { Accounts } from 'meteor/accounts-base';

export const Register = ({ goToLanding }) => {
  const [register, setRegister] = useState({ fullname: '', email: '', password: '', dob: '' });
  const [error, setError] = useState('');

  const handleChange = (e) => {
    const { name, value } = e.target;
    setRegister({ ...register, [name]: value });
  };

  const handleRegister = (e) => {
    e.preventDefault();
    setError(''); // Clear any previous errors

    // Validation
    if (!register.fullname.trim()) {
      setError('Full name is required');
      return;
    }
    const emailRegex = /^[^@]+@[^@]+\.[^@]+$/;
    if (!emailRegex.test(register.email)) {
      setError('Please enter a valid email');
      return;
    }
    if (register.password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }
    if (!register.dob) {
      setError('Date of birth is required');
      return;
    }

    Accounts.createUser(
      {
        email: register.email,
        password: register.password,
        profile: {
          fullname: register.fullname,
          dob: register.dob,
        },
      },
      (err) => {
        if (err) {
          setError(err.reason || 'Registration failed');
        } else {
          setError('');
          setRegister({ fullname: '', email: '', password: '', dob: '' });
          // The App component will automatically redirect to dashboard after successful registration
        }
      }
    );
  };

  return (
    <div className="landing">
      <h1 className="title brand">Roylia</h1>
      <p className="tagline">Rule with your mind.</p>
      <form className="card" onSubmit={handleRegister}>
        <h2>Register</h2>
        {error && <p className="error">{error}</p>}
        <label>
          Full Name
          <input
            type="text"
            name="fullname"
            value={register.fullname}
            onChange={handleChange}
            required
          />
        </label>
        <label>
          Email
          <input
            type="email"
            name="email"
            value={register.email}
            onChange={handleChange}
            pattern="[^@]+@[^@]+\.[^@]+"
            required
          />
        </label>
        <label>
          Password
          <input
            type="password"
            name="password"
            value={register.password}
            onChange={handleChange}
            minLength={6}
            required
          />
        </label>
        <label>
          Date of Birth
          <input
            type="date"
            name="dob"
            value={register.dob}
            onChange={handleChange}
            required
          />
        </label>
        <button type="submit" className="btn">Register</button>
      </form>
      <div className="forget">
        <a href="#" onClick={goToLanding}>Back to Login</a>
      </div>
    </div>
  );
};
