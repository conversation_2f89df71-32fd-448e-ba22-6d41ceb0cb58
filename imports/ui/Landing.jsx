import React, { useState } from 'react';

export const Landing = ({ goToRegister }) => {
  const [login, setLogin] = useState({ email: '', password: '' });

  const handleLoginChange = (e) => {
    const { name, value } = e.target;
    setLogin({ ...login, [name]: value });
  };

  const handleLogin = (e) => {
    e.preventDefault();
    console.log('Login info', login);
  };

  return (
    <div className="landing">
      <h1 className="title brand"><PERSON><PERSON></h1>
      <p className="tagline">The social network for the mentally royal</p>
      <div className="forms">
        <form className="card" onSubmit={handleLogin}>
          <h2>Login</h2>
          <label>
            Email
            <input
              type="email"
              name="email"
              value={login.email}
              onChange={handleLoginChange}
              required
            />
          </label>
          <label>
            Password
            <input
              type="password"
              name="password"
              value={login.password}
              onChange={handleLoginChange}
              required
            />
          </label>
          <button type="submit" className="btn">Login</button>
          <div className="forget">
            <a href="#">Forgot password?</a>
          </div>
        </form>
      </div>
      <div className="forget">
        <a href="#" onClick={goToRegister}>Don't have an account? Create one.</a>
      </div>
    </div>
  );
};
