@import url('https://cdn.jsdelivr.net/npm/@fontsource/geist-mono/index.css');

:root {
  --primary-color: #cc00cc;
  --primary-light: #e066e0;
  --primary-lighter: #f5ccf5;
  --card-bg: #ffe6ff;
  --text-color: #333;
  --bg-color: #fff;
}

[data-theme='dark'] {
  --primary-color: #cc00cc;
  --primary-light: #e066e0;
  --primary-lighter: #4d004d;
  --card-bg: #2d002d;
  --text-color: #f0f0f0;
  --bg-color: #121212;
}

body {
  margin: 0;
  padding: 10px;
  font-family: Arial, Helvetica, sans-serif, monospace;
  background-color: var(--bg-color);
  color: var(--text-color);
  min-height: 100vh;
}

.title {
  color: var(--primary-color);
}

.title.brand {
  font-size: 3rem;
  margin: 0.5em 0 0.2em;
  text-align: center;
}

.tagline {
  margin-bottom: 20px;
  font-size: 1.25rem;
  text-align: center;
  color: var(--primary-light);
}

.btn {
  background-color: var(--primary-color);
  color: #fff;
  border: 1px solid var(--primary-light);
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease, transform 0.1s ease;
}

.btn:hover {
  background-color: var(--primary-light);
  transform: translateY(-2px);
}

.card {
  background-color: var(--card-bg);
  border: 1px solid var(--primary-lighter);
  box-shadow: 0 2px 8px var(--primary-lighter);
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.card:hover {
  transform: translateY(-4px);
}

.landing {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  min-height: calc(100vh - 80px);
  justify-content: center;
}


.forms {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
  width: 100%;
  max-width: 800px;
}

.forms form {
  flex: 1 1 300px;
}

label {
  display: block;
  margin-bottom: 8px;
}

input {
  width: 100%;
  padding: 8px;
  margin-top: 4px;
  margin-bottom: 12px;
  border: 1px solid var(--primary-light);
  border-radius: 4px;
  background-color: var(--bg-color);
  color: var(--text-color);
}

.forget {
  margin-top: 8px;
  font-size: 0.9em;
}

.forget a {
  color: var(--primary-color);
  text-decoration: none;
}

.forget a:hover {
  text-decoration: underline;
}
