@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

:root {
  --primary-color: #cc00cc;
  --primary-light: #e066e0;
  --primary-dark: #990099;
  --primary-lighter: #f5ccf5;
  --primary-gradient: linear-gradient(135deg, #cc00cc 0%, #e066e0 100%);
  --card-bg: linear-gradient(145deg, #ffffff 0%, #fafafa 100%);
  --card-shadow: 0 10px 40px rgba(204, 0, 204, 0.1);
  --card-hover-shadow: 0 20px 60px rgba(204, 0, 204, 0.15);
  --text-color: #1a1a1a;
  --text-secondary: #666666;
  --bg-color: #f8f9fa;
  --input-bg: #ffffff;
  --input-border: #e1e5e9;
  --input-focus: #cc00cc;
}

[data-theme='dark'] {
  --primary-color: #cc00cc;
  --primary-light: #e066e0;
  --primary-dark: #990099;
  --primary-lighter: #4d004d;
  --primary-gradient: linear-gradient(135deg, #cc00cc 0%, #e066e0 100%);
  --card-bg: linear-gradient(145deg, #1e1e1e 0%, #2a2a2a 100%);
  --card-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  --card-hover-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  --text-color: #ffffff;
  --text-secondary: #b0b0b0;
  --bg-color: #0f0f0f;
  --input-bg: #2a2a2a;
  --input-border: #404040;
  --input-focus: #e066e0;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-color);
  color: var(--text-color);
  min-height: 100vh;
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
  overflow-x: hidden;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(204, 0, 204, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(224, 102, 224, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(204, 0, 204, 0.02) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

.app {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.theme-toggle {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: var(--card-bg);
  border: 2px solid var(--input-border);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--card-shadow);
  z-index: 1000;
}

.theme-toggle:hover {
  transform: scale(1.1) rotate(10deg);
  box-shadow: var(--card-hover-shadow);
}

.title {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 900;
  letter-spacing: -0.02em;
}

.title.brand {
  font-size: clamp(3rem, 8vw, 6rem);
  margin: 0 0 0.3em 0;
  text-align: center;
  line-height: 0.9;
}

.tagline {
  margin-bottom: 3rem;
  font-size: clamp(1.1rem, 3vw, 1.4rem);
  text-align: center;
  color: var(--text-secondary);
  font-weight: 500;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.btn {
  background: var(--primary-gradient);
  color: #ffffff;
  border: none;
  padding: 16px 32px;
  cursor: pointer;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.02em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(204, 0, 204, 0.3);
  position: relative;
  overflow: hidden;
  width: 100%;
  text-transform: uppercase;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(204, 0, 204, 0.4);
}

.btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(204, 0, 204, 0.3);
}

.card {
  background: var(--card-bg);
  border: 1px solid var(--input-border);
  box-shadow: var(--card-shadow);
  padding: 2.5rem;
  margin-bottom: 2rem;
  border-radius: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  width: 100%;
  max-width: 420px;
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: var(--card-hover-shadow);
}

.card h2 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 2rem 0;
  text-align: center;
  color: var(--text-color);
  letter-spacing: -0.01em;
}

.landing {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  min-height: 100vh;
  justify-content: center;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.forms {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  gap: 2rem;
}

.forms form {
  width: 100%;
  max-width: 420px;
}

label {
  display: block;
  margin-bottom: 1.5rem;
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.95rem;
  letter-spacing: 0.01em;
}

input {
  width: 100%;
  padding: 16px 20px;
  margin-top: 8px;
  border: 2px solid var(--input-border);
  border-radius: 12px;
  background: var(--input-bg);
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 400;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
}

input:focus {
  border-color: var(--input-focus);
  box-shadow: 0 0 0 3px rgba(204, 0, 204, 0.1);
  transform: translateY(-1px);
}

input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

.forget {
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.95rem;
}

.forget a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.forget a:hover {
  color: var(--primary-light);
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .landing {
    padding: 1rem;
  }

  .title.brand {
    font-size: clamp(2.5rem, 10vw, 4rem);
  }

  .card {
    padding: 2rem;
    margin-bottom: 1.5rem;
  }

  .tagline {
    margin-bottom: 2rem;
    font-size: clamp(1rem, 4vw, 1.2rem);
  }
}

@media (max-width: 480px) {
  .card {
    padding: 1.5rem;
    border-radius: 16px;
  }

  input {
    padding: 14px 16px;
  }

  .btn {
    padding: 14px 24px;
  }
}
