{"name": "meteor-app", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "meteor-app", "dependencies": {"@babel/runtime": "^7.20.7", "meteor-node-stubs": "^1.2.5", "react": "^18.2.0", "react-dom": "^18.2.0"}}, "node_modules/@babel/runtime": {"version": "7.27.6", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "integrity": "sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/bn.js": {"version": "4.12.2", "resolved": "https://registry.npmjs.org/bn.js/-/bn.js-4.12.2.tgz", "integrity": "sha512-n4DSx829VRTRByMRGdjQ9iqsN0Bh4OolPsFnaZBLcbi8iXcB+kJ9s7EnRt4wILZNV3kPLHkRVfOc/HvhC3ovDw==", "license": "MIT"}, "node_modules/brorand": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/brorand/-/brorand-1.1.0.tgz", "integrity": "sha512-cKV8tMCEpQs4hK/ik71d6LrPOnpkpGBR0wzxqr68g2m/LB2GxVYQroAjMJZRVM1Y4BCjCKc3vAamxSzOY2RP+w==", "license": "MIT"}, "node_modules/elliptic": {"version": "6.6.1", "resolved": "https://registry.npmjs.org/elliptic/-/elliptic-6.6.1.tgz", "integrity": "sha512-RaddvvMatK2LJHqFJ+YA4WysVN5Ita9E35botqIYspQ4TkRAlCicdzKOjlyv/1Za5RyTNn7di//eEV0uTAfe3g==", "license": "MIT", "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/hash.js": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/hash.js/-/hash.js-1.1.7.tgz", "integrity": "sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA==", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "node_modules/hmac-drbg": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/hmac-drbg/-/hmac-drbg-1.0.1.tgz", "integrity": "sha512-Tti3gMqLdZfhOQY1Mzf/AanLiqh1WTiJgEj26ZuYQ9fbkLomzGchCws4FyrSd4VkpBfiNhaE1On+lOz894jvXg==", "license": "MIT", "dependencies": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "license": "ISC"}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "license": "MIT"}, "node_modules/loose-envify": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/meteor-node-stubs": {"version": "1.2.21", "resolved": "https://registry.npmjs.org/meteor-node-stubs/-/meteor-node-stubs-1.2.21.tgz", "integrity": "sha512-ClfqnJKd9RhUONKizBQWumMAG5VXTRNANv4wjZftjYZkv/XAoBukgmI7d6DomOQ/XA+xtv+USpmHeuWhqVGoqg==", "bundleDependencies": ["@meteorjs/crypto-browserify", "assert", "browserify-zlib", "buffer", "console-browserify", "constants-browserify", "domain-browser", "events", "https-browserify", "os-browserify", "path-browserify", "process", "punycode", "querystring-es3", "readable-stream", "stream-browserify", "stream-http", "string_decoder", "timers-browserify", "tty-browserify", "url", "util", "vm-browserify"], "license": "MIT", "dependencies": {"@meteorjs/crypto-browserify": "^3.12.1", "assert": "^2.1.0", "browserify-zlib": "^0.2.0", "buffer": "^5.7.1", "console-browserify": "^1.2.0", "constants-browserify": "^1.0.0", "domain-browser": "^4.23.0", "elliptic": "^6.6.1", "events": "^3.3.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "punycode": "^1.4.1", "querystring-es3": "^0.2.1", "readable-stream": "^3.6.2", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "string_decoder": "^1.3.0", "timers-browserify": "^2.0.12", "tty-browserify": "0.0.1", "url": "^0.11.4", "util": "^0.12.5", "vm-browserify": "^1.1.2"}}, "node_modules/meteor-node-stubs/node_modules/@meteorjs/browserify-sign": {"version": "4.2.6", "inBundle": true, "license": "ISC", "dependencies": {"bn.js": "^5.2.1", "brorand": "^1.1.0", "browserify-rsa": "^4.1.0", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "hash-base": "~3.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1", "parse-asn1": "^5.1.7", "readable-stream": "^2.3.8", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.12"}}, "node_modules/meteor-node-stubs/node_modules/@meteorjs/browserify-sign/node_modules/isarray": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/@meteorjs/browserify-sign/node_modules/readable-stream": {"version": "2.3.8", "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/meteor-node-stubs/node_modules/@meteorjs/browserify-sign/node_modules/readable-stream/node_modules/safe-buffer": {"version": "5.1.2", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/@meteorjs/browserify-sign/node_modules/string_decoder": {"version": "1.1.1", "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/meteor-node-stubs/node_modules/@meteorjs/browserify-sign/node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.1.2", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/@meteorjs/create-ecdh": {"version": "4.0.5", "inBundle": true, "license": "MIT", "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/meteor-node-stubs/node_modules/@meteorjs/create-ecdh/node_modules/bn.js": {"version": "4.12.2", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/@meteorjs/crypto-browserify": {"version": "3.12.4", "inBundle": true, "license": "MIT", "dependencies": {"@meteorjs/browserify-sign": "^4.2.3", "@meteorjs/create-ecdh": "^4.0.4", "browserify-cipher": "^1.0.1", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "diffie-hellman": "^5.0.3", "hash-base": "~3.0.4", "inherits": "^2.0.4", "pbkdf2": "^3.1.2", "public-encrypt": "^4.0.3", "randombytes": "^2.1.0", "randomfill": "^1.0.4"}, "engines": {"node": ">= 0.10"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/asn1.js": {"version": "4.10.1", "inBundle": true, "license": "MIT", "dependencies": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "node_modules/meteor-node-stubs/node_modules/asn1.js/node_modules/bn.js": {"version": "4.12.2", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/assert": {"version": "2.1.0", "inBundle": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "is-nan": "^1.3.2", "object-is": "^1.1.5", "object.assign": "^4.1.4", "util": "^0.12.5"}}, "node_modules/meteor-node-stubs/node_modules/available-typed-arrays": {"version": "1.0.7", "inBundle": true, "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/bn.js": {"version": "5.2.2", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/brorand": {"version": "1.1.0", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/browserify-aes": {"version": "1.2.0", "inBundle": true, "license": "MIT", "dependencies": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "node_modules/meteor-node-stubs/node_modules/browserify-cipher": {"version": "1.0.1", "inBundle": true, "license": "MIT", "dependencies": {"browserify-aes": "^1.0.4", "browserify-des": "^1.0.0", "evp_bytestokey": "^1.0.0"}}, "node_modules/meteor-node-stubs/node_modules/browserify-des": {"version": "1.0.2", "inBundle": true, "license": "MIT", "dependencies": {"cipher-base": "^1.0.1", "des.js": "^1.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/meteor-node-stubs/node_modules/browserify-rsa": {"version": "4.1.1", "inBundle": true, "license": "MIT", "dependencies": {"bn.js": "^5.2.1", "randombytes": "^2.1.0", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/meteor-node-stubs/node_modules/browserify-zlib": {"version": "0.2.0", "inBundle": true, "license": "MIT", "dependencies": {"pako": "~1.0.5"}}, "node_modules/meteor-node-stubs/node_modules/buffer": {"version": "5.7.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "inBundle": true, "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/meteor-node-stubs/node_modules/buffer-xor": {"version": "1.0.3", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/builtin-status-codes": {"version": "3.0.0", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/call-bind": {"version": "1.0.8", "inBundle": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/call-bind-apply-helpers": {"version": "1.0.2", "inBundle": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/meteor-node-stubs/node_modules/call-bound": {"version": "1.0.4", "inBundle": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/cipher-base": {"version": "1.0.6", "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.4", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/meteor-node-stubs/node_modules/console-browserify": {"version": "1.2.0", "inBundle": true}, "node_modules/meteor-node-stubs/node_modules/constants-browserify": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/core-util-is": {"version": "1.0.3", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/create-hash": {"version": "1.2.0", "inBundle": true, "license": "MIT", "dependencies": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}, "node_modules/meteor-node-stubs/node_modules/create-hmac": {"version": "1.1.7", "inBundle": true, "license": "MIT", "dependencies": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "node_modules/meteor-node-stubs/node_modules/define-data-property": {"version": "1.1.4", "inBundle": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/define-properties": {"version": "1.2.1", "inBundle": true, "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/des.js": {"version": "1.1.0", "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "node_modules/meteor-node-stubs/node_modules/diffie-hellman": {"version": "5.0.3", "inBundle": true, "license": "MIT", "dependencies": {"bn.js": "^4.1.0", "miller-rabin": "^4.0.0", "randombytes": "^2.0.0"}}, "node_modules/meteor-node-stubs/node_modules/diffie-hellman/node_modules/bn.js": {"version": "4.12.2", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/domain-browser": {"version": "4.23.0", "inBundle": true, "license": "Artistic-2.0", "engines": {"node": ">=10"}, "funding": {"url": "https://bevry.me/fund"}}, "node_modules/meteor-node-stubs/node_modules/dunder-proto": {"version": "1.0.1", "inBundle": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/meteor-node-stubs/node_modules/es-define-property": {"version": "1.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/meteor-node-stubs/node_modules/es-errors": {"version": "1.3.0", "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/meteor-node-stubs/node_modules/es-object-atoms": {"version": "1.1.1", "inBundle": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/meteor-node-stubs/node_modules/events": {"version": "3.3.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/meteor-node-stubs/node_modules/evp_bytestokey": {"version": "1.0.3", "inBundle": true, "license": "MIT", "dependencies": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}, "node_modules/meteor-node-stubs/node_modules/for-each": {"version": "0.3.5", "inBundle": true, "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/function-bind": {"version": "1.1.2", "inBundle": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/get-intrinsic": {"version": "1.3.0", "inBundle": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/get-proto": {"version": "1.0.1", "inBundle": true, "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/meteor-node-stubs/node_modules/gopd": {"version": "1.2.0", "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/has-property-descriptors": {"version": "1.0.2", "inBundle": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/has-symbols": {"version": "1.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/has-tostringtag": {"version": "1.0.2", "inBundle": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/hash-base": {"version": "3.0.5", "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.4", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/meteor-node-stubs/node_modules/hash.js": {"version": "1.1.7", "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "node_modules/meteor-node-stubs/node_modules/hasown": {"version": "2.0.2", "inBundle": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/meteor-node-stubs/node_modules/hmac-drbg": {"version": "1.0.1", "inBundle": true, "license": "MIT", "dependencies": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/meteor-node-stubs/node_modules/https-browserify": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "inBundle": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/meteor-node-stubs/node_modules/inherits": {"version": "2.0.4", "inBundle": true, "license": "ISC"}, "node_modules/meteor-node-stubs/node_modules/is-arguments": {"version": "1.2.0", "inBundle": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/is-callable": {"version": "1.2.7", "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/is-generator-function": {"version": "1.1.0", "inBundle": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/is-nan": {"version": "1.3.2", "inBundle": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.0", "define-properties": "^1.1.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/is-regex": {"version": "1.2.1", "inBundle": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/is-typed-array": {"version": "1.1.15", "inBundle": true, "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/isarray": {"version": "2.0.5", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/math-intrinsics": {"version": "1.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/meteor-node-stubs/node_modules/md5.js": {"version": "1.3.5", "inBundle": true, "license": "MIT", "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/meteor-node-stubs/node_modules/miller-rabin": {"version": "4.0.1", "inBundle": true, "license": "MIT", "dependencies": {"bn.js": "^4.0.0", "brorand": "^1.0.1"}, "bin": {"miller-rabin": "bin/miller-rabin"}}, "node_modules/meteor-node-stubs/node_modules/miller-rabin/node_modules/bn.js": {"version": "4.12.2", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/minimalistic-assert": {"version": "1.0.1", "inBundle": true, "license": "ISC"}, "node_modules/meteor-node-stubs/node_modules/minimalistic-crypto-utils": {"version": "1.0.1", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/object-inspect": {"version": "1.13.4", "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/object-is": {"version": "1.1.6", "inBundle": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/object-keys": {"version": "1.1.1", "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/meteor-node-stubs/node_modules/object.assign": {"version": "4.1.7", "inBundle": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/os-browserify": {"version": "0.3.0", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/pako": {"version": "1.0.11", "inBundle": true, "license": "(MIT AND Zlib)"}, "node_modules/meteor-node-stubs/node_modules/parse-asn1": {"version": "5.1.7", "inBundle": true, "license": "ISC", "dependencies": {"asn1.js": "^4.10.1", "browserify-aes": "^1.2.0", "evp_bytestokey": "^1.0.3", "hash-base": "~3.0", "pbkdf2": "^3.1.2", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/meteor-node-stubs/node_modules/path-browserify": {"version": "1.0.1", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/pbkdf2": {"version": "3.1.3", "inBundle": true, "license": "MIT", "dependencies": {"create-hash": "~1.1.3", "create-hmac": "^1.1.7", "ripemd160": "=2.0.1", "safe-buffer": "^5.2.1", "sha.js": "^2.4.11", "to-buffer": "^1.2.0"}, "engines": {"node": ">=0.12"}}, "node_modules/meteor-node-stubs/node_modules/pbkdf2/node_modules/create-hash": {"version": "1.1.3", "inBundle": true, "license": "MIT", "dependencies": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "sha.js": "^2.4.0"}}, "node_modules/meteor-node-stubs/node_modules/pbkdf2/node_modules/hash-base": {"version": "2.0.2", "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1"}}, "node_modules/meteor-node-stubs/node_modules/pbkdf2/node_modules/ripemd160": {"version": "2.0.1", "inBundle": true, "license": "MIT", "dependencies": {"hash-base": "^2.0.0", "inherits": "^2.0.1"}}, "node_modules/meteor-node-stubs/node_modules/possible-typed-array-names": {"version": "1.1.0", "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/meteor-node-stubs/node_modules/process": {"version": "0.11.10", "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/meteor-node-stubs/node_modules/process-nextick-args": {"version": "2.0.1", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/public-encrypt": {"version": "4.0.3", "inBundle": true, "license": "MIT", "dependencies": {"bn.js": "^4.1.0", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "parse-asn1": "^5.0.0", "randombytes": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/meteor-node-stubs/node_modules/public-encrypt/node_modules/bn.js": {"version": "4.12.2", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/punycode": {"version": "1.4.1", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/qs": {"version": "6.14.0", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/querystring-es3": {"version": "0.2.1", "inBundle": true, "engines": {"node": ">=0.4.x"}}, "node_modules/meteor-node-stubs/node_modules/randombytes": {"version": "2.1.0", "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/meteor-node-stubs/node_modules/randomfill": {"version": "1.0.4", "inBundle": true, "license": "MIT", "dependencies": {"randombytes": "^2.0.5", "safe-buffer": "^5.1.0"}}, "node_modules/meteor-node-stubs/node_modules/readable-stream": {"version": "3.6.2", "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/meteor-node-stubs/node_modules/ripemd160": {"version": "2.0.2", "inBundle": true, "license": "MIT", "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "node_modules/meteor-node-stubs/node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/safe-regex-test": {"version": "1.1.0", "inBundle": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/set-function-length": {"version": "1.2.2", "inBundle": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/meteor-node-stubs/node_modules/setimmediate": {"version": "1.0.5", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/sha.js": {"version": "2.4.11", "inBundle": true, "license": "(MIT AND BSD-3-<PERSON><PERSON>)", "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "bin": {"sha.js": "bin.js"}}, "node_modules/meteor-node-stubs/node_modules/side-channel": {"version": "1.1.0", "inBundle": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/side-channel-list": {"version": "1.0.0", "inBundle": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/side-channel-map": {"version": "1.0.1", "inBundle": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/side-channel-weakmap": {"version": "1.0.2", "inBundle": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/stream-browserify": {"version": "3.0.0", "inBundle": true, "license": "MIT", "dependencies": {"inherits": "~2.0.4", "readable-stream": "^3.5.0"}}, "node_modules/meteor-node-stubs/node_modules/stream-http": {"version": "3.2.0", "inBundle": true, "license": "MIT", "dependencies": {"builtin-status-codes": "^3.0.0", "inherits": "^2.0.4", "readable-stream": "^3.6.0", "xtend": "^4.0.2"}}, "node_modules/meteor-node-stubs/node_modules/string_decoder": {"version": "1.3.0", "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/meteor-node-stubs/node_modules/timers-browserify": {"version": "2.0.12", "inBundle": true, "license": "MIT", "dependencies": {"setimmediate": "^1.0.4"}, "engines": {"node": ">=0.6.0"}}, "node_modules/meteor-node-stubs/node_modules/to-buffer": {"version": "1.2.1", "inBundle": true, "license": "MIT", "dependencies": {"isarray": "^2.0.5", "safe-buffer": "^5.2.1", "typed-array-buffer": "^1.0.3"}, "engines": {"node": ">= 0.4"}}, "node_modules/meteor-node-stubs/node_modules/tty-browserify": {"version": "0.0.1", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/typed-array-buffer": {"version": "1.0.3", "inBundle": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}}, "node_modules/meteor-node-stubs/node_modules/url": {"version": "0.11.4", "inBundle": true, "license": "MIT", "dependencies": {"punycode": "^1.4.1", "qs": "^6.12.3"}, "engines": {"node": ">= 0.4"}}, "node_modules/meteor-node-stubs/node_modules/util": {"version": "0.12.5", "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "is-arguments": "^1.0.4", "is-generator-function": "^1.0.7", "is-typed-array": "^1.1.3", "which-typed-array": "^1.1.2"}}, "node_modules/meteor-node-stubs/node_modules/util-deprecate": {"version": "1.0.2", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/vm-browserify": {"version": "1.1.2", "inBundle": true, "license": "MIT"}, "node_modules/meteor-node-stubs/node_modules/which-typed-array": {"version": "1.1.19", "inBundle": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/meteor-node-stubs/node_modules/xtend": {"version": "4.0.2", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "integrity": "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==", "license": "ISC"}, "node_modules/minimalistic-crypto-utils": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz", "integrity": "sha512-JIYlbt6g8i5jKfJ3xz7rF0LXmv2TkDxBLUkiBeZ7bAx4GnnNMr8xFpGnOxn6GhTEHx3SjRrZEoU+j04prX1ktg==", "license": "MIT"}, "node_modules/react": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react/-/react-18.3.1.tgz", "integrity": "sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/react-dom": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-18.3.1.tgz", "integrity": "sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "scheduler": "^0.23.2"}, "peerDependencies": {"react": "^18.3.1"}}, "node_modules/scheduler": {"version": "0.23.2", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz", "integrity": "sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}}}}